#!/usr/bin/env python3
"""
简化的Elasticsearch Agent启动脚本
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any, List, AsyncGenerator
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 检查依赖
try:
    from fastmcp import Client
    from fastmcp.client.transports import NpxStdioTransport
    from openai import OpenAI
    from fastapi import FastAPI, WebSocket, WebSocketDisconnect
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import HTMLResponse
    from pydantic import BaseModel
    import uvicorn
except ImportError as e:
    print(f"❌ 缺少依赖: {e}")
    print("请运行: pip install fastmcp openai fastapi uvicorn websockets")
    sys.exit(1)

# 配置
CONFIG = {
    "llm": {
        "base_url": "http://ai.ai.iot.chinamobile.com/imaas/v1",
        "api_key": "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db",
        "model": "DeepSeek-R1",
        "temperature": 0.7,
        "max_tokens": 4000
    },
    "mcp": {
        "ES_URL": "https://medusa.iotxmm.com:19203",
        "ES_USERNAME": "elastic",
        "ES_PASSWORD": "elastic$2020$search",
        "ES_VERSION": "6"
    },
    "server": {
        "host": "0.0.0.0",
        "port": 8081
    }
}

SYSTEM_PROMPT = """你是一个专业的Elasticsearch数据查询和分析助手。你可以帮助用户：

1. **索引管理**：列出索引、获取映射、分析分片
2. **数据搜索**：执行各种Elasticsearch查询
3. **数据分析**：提供统计分析和趋势识别
4. **查询优化**：建议更高效的查询方式

**可用工具**：
- list_indices: 列出所有索引
- get_mappings: 获取索引映射
- search: 执行搜索查询
- get_shards: 获取分片信息

请告诉我你想要查询或分析什么数据！"""

class ElasticsearchAgent:
    """Elasticsearch智能助手"""

    def __init__(self):
        self.llm_config = CONFIG["llm"]
        # 创建OpenAI客户端，指向自定义API端点
        self.llm_client = OpenAI(
            api_key=self.llm_config["api_key"],
            base_url=self.llm_config["base_url"]
        )
        self.mcp_client = None
        self.conversation_history = []
        self.available_tools = []
        
    async def initialize(self):
        """初始化Agent"""
        try:
            logger.info("🚀 初始化Elasticsearch Agent...")
            
            # 创建传输
            transport = NpxStdioTransport(
                package="@elastic/mcp-server-elasticsearch",
                args=["-y"]
            )
            transport.env = CONFIG["mcp"]
            
            # 连接MCP服务器
            self.mcp_client = Client(transport)
            await self.mcp_client.__aenter__()
            await self.mcp_client.ping()
            
            # 获取工具
            tools = await self.mcp_client.list_tools()
            self.available_tools = [
                {"name": tool.name, "description": tool.description}
                for tool in tools
            ]
            
            logger.info(f"✅ Agent初始化完成! 加载了 {len(self.available_tools)} 个工具")
            for tool in self.available_tools:
                logger.info(f"  - {tool['name']}: {tool['description']}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Agent初始化失败: {e}")
            return False
    
    async def chat(self, user_message: str) -> AsyncGenerator[str, None]:
        """对话"""
        try:
            # 添加用户消息
            self.conversation_history.append({
                "role": "user",
                "content": user_message,
                "timestamp": datetime.now().isoformat()
            })

            # 构建消息
            messages = [{"role": "system", "content": SYSTEM_PROMPT}]

            # 添加工具信息
            tools_info = "\n\n**当前可用工具**:\n"
            for tool in self.available_tools:
                tools_info += f"- {tool['name']}: {tool['description']}\n"
            messages[0]["content"] += tools_info

            # 添加历史对话
            for msg in self.conversation_history[-10:]:  # 最近10条
                messages.append({"role": msg["role"], "content": msg["content"]})

            # 使用OpenAI客户端进行流式调用
            completion = self.llm_client.chat.completions.create(
                model=self.llm_config["model"],
                messages=messages,
                temperature=self.llm_config["temperature"],
                max_tokens=self.llm_config["max_tokens"],
                stream=True
            )

            full_response = ""
            for chunk in completion:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_response += content
                    yield content

            # 保存回复
            self.conversation_history.append({
                "role": "assistant",
                "content": full_response,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            error_msg = f"❌ 对话失败: {e}"
            logger.error(error_msg)
            yield error_msg
    
    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具"""
        try:
            logger.info(f"🛠️ 执行工具: {tool_name} with {parameters}")
            result = await self.mcp_client.call_tool(tool_name, parameters)
            return {
                "success": True,
                "result": result[0].text if result else None
            }
        except Exception as e:
            logger.error(f"❌ 工具执行失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def close(self):
        """关闭"""
        if self.mcp_client:
            await self.mcp_client.__aexit__(None, None, None)

# 全局Agent实例
agent = ElasticsearchAgent()

# FastAPI应用
app = FastAPI(title="Elasticsearch Agent")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class ChatRequest(BaseModel):
    message: str

class ToolRequest(BaseModel):
    tool_name: str
    parameters: Dict[str, Any] = {}

# 连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def send_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

manager = ConnectionManager()

@app.on_event("startup")
async def startup_event():
    """启动时初始化Agent"""
    success = await agent.initialize()
    if not success:
        raise Exception("Agent初始化失败")

@app.on_event("shutdown")
async def shutdown_event():
    """关闭时清理"""
    await agent.close()

@app.get("/")
async def get_frontend():
    """返回前端页面"""
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elasticsearch Agent</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; display: flex; justify-content: center; align-items: center;
        }
        .container { 
            width: 90%; max-width: 800px; height: 80vh; background: white;
            border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex; flex-direction: column; overflow: hidden;
        }
        .header { 
            padding: 20px; background: #343a40; color: white; text-align: center;
        }
        .chat-container { 
            flex: 1; overflow-y: auto; padding: 20px; background: #f8f9fa;
        }
        .message { 
            margin-bottom: 15px; display: flex; gap: 10px;
        }
        .message.user { flex-direction: row-reverse; }
        .message-avatar { 
            width: 32px; height: 32px; border-radius: 50%; 
            display: flex; align-items: center; justify-content: center;
            font-weight: bold; color: white; font-size: 12px;
        }
        .message.user .message-avatar { background: #007bff; }
        .message.assistant .message-avatar { background: #28a745; }
        .message-content { 
            max-width: 70%; padding: 10px 15px; border-radius: 15px;
            line-height: 1.4; font-size: 14px; white-space: pre-wrap;
        }
        .message.user .message-content { background: #007bff; color: white; }
        .message.assistant .message-content { background: white; border: 1px solid #dee2e6; }
        .input-container { 
            padding: 20px; background: white; border-top: 1px solid #dee2e6;
        }
        .input-group { display: flex; gap: 10px; }
        .input-field { 
            flex: 1; padding: 10px 15px; border: 2px solid #dee2e6;
            border-radius: 20px; outline: none; font-size: 14px;
        }
        .input-field:focus { border-color: #007bff; }
        .send-button { 
            padding: 10px 20px; background: #007bff; color: white;
            border: none; border-radius: 20px; cursor: pointer;
        }
        .send-button:hover { background: #0056b3; }
        .send-button:disabled { background: #6c757d; cursor: not-allowed; }
        .quick-actions { 
            display: flex; gap: 8px; margin-bottom: 10px; flex-wrap: wrap;
        }
        .quick-action { 
            padding: 5px 10px; background: #e9ecef; border: none;
            border-radius: 15px; font-size: 12px; cursor: pointer;
        }
        .quick-action:hover { background: #dee2e6; }
        .status { padding: 10px; text-align: center; font-size: 12px; }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Elasticsearch Agent</h1>
            <p>智能数据查询助手</p>
        </div>
        
        <div id="status" class="status disconnected">正在连接...</div>
        
        <div class="chat-container" id="chat-container">
            <div class="message assistant">
                <div class="message-avatar">🤖</div>
                <div class="message-content">你好！我是Elasticsearch智能助手。我可以帮助你查询和分析数据。请告诉我你想要查询什么！</div>
            </div>
        </div>
        
        <div class="input-container">
            <div class="quick-actions">
                <button class="quick-action" onclick="sendQuickMessage('列出所有索引')">列出索引</button>
                <button class="quick-action" onclick="sendQuickMessage('搜索最近的数据')">最近数据</button>
                <button class="quick-action" onclick="sendQuickMessage('分析集群状态')">集群状态</button>
                <button class="quick-action" onclick="clearChat()">清空对话</button>
            </div>
            <div class="input-group">
                <input type="text" id="message-input" class="input-field" placeholder="请输入你的问题..." />
                <button id="send-button" class="send-button" onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        const chatContainer = document.getElementById('chat-container');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const status = document.getElementById('status');
        let currentMessage = null;

        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:8081/ws');
            
            ws.onopen = function() {
                status.textContent = '已连接';
                status.className = 'status connected';
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'assistant_start') {
                    currentMessage = addMessage('assistant', '');
                } else if (data.type === 'assistant_chunk') {
                    if (currentMessage) {
                        currentMessage.textContent += data.content;
                        scrollToBottom();
                    }
                } else if (data.type === 'assistant_end') {
                    currentMessage = null;
                    enableInput();
                }
            };
            
            ws.onclose = function() {
                status.textContent = '连接断开';
                status.className = 'status disconnected';
                setTimeout(connectWebSocket, 3000);
            };
            
            ws.onerror = function() {
                status.textContent = '连接错误';
                status.className = 'status disconnected';
            };
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || !ws || ws.readyState !== WebSocket.OPEN) return;
            
            addMessage('user', message);
            messageInput.value = '';
            disableInput();
            
            ws.send(JSON.stringify({ message: message }));
        }

        function sendQuickMessage(message) {
            messageInput.value = message;
            sendMessage();
        }

        function addMessage(role, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            const avatar = role === 'user' ? '👤' : '🤖';
            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">${content}</div>
            `;
            chatContainer.appendChild(messageDiv);
            scrollToBottom();
            return messageDiv.querySelector('.message-content');
        }

        function scrollToBottom() {
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function disableInput() {
            messageInput.disabled = true;
            sendButton.disabled = true;
        }

        function enableInput() {
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();
        }

        function clearChat() {
            const messages = chatContainer.querySelectorAll('.message');
            messages.forEach((msg, index) => {
                if (index > 0) msg.remove();
            });
        }

        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // 初始化
        connectWebSocket();
        window.sendQuickMessage = sendQuickMessage;
        window.clearChat = clearChat;
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "tools_count": len(agent.available_tools)
    }

@app.get("/tools")
async def get_tools():
    """获取工具"""
    return {"tools": agent.available_tools}

@app.post("/chat")
async def chat(request: ChatRequest):
    """对话接口"""
    response = ""
    async for chunk in agent.chat(request.message):
        response += chunk
    return {"response": response}

@app.post("/tool")
async def execute_tool(request: ToolRequest):
    """执行工具"""
    return await agent.execute_tool(request.tool_name, request.parameters)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket对话"""
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)
            user_message = message_data.get("message", "")
            
            if user_message:
                # 发送用户消息确认
                await manager.send_message(
                    json.dumps({"type": "user_message", "content": user_message}),
                    websocket
                )
                
                # 流式发送回复
                await manager.send_message(
                    json.dumps({"type": "assistant_start"}),
                    websocket
                )
                
                async for chunk in agent.chat(user_message):
                    await manager.send_message(
                        json.dumps({"type": "assistant_chunk", "content": chunk}),
                        websocket
                    )
                
                await manager.send_message(
                    json.dumps({"type": "assistant_end"}),
                    websocket
                )
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        manager.disconnect(websocket)

if __name__ == "__main__":
    print("🚀 启动Elasticsearch Agent")
    print("=" * 50)
    print("🌐 Web界面: http://localhost:8081")
    print("📊 API文档: http://localhost:8081/docs")
    print("=" * 50)
    print("💡 确保已安装:")
    print("  - npm install -g @elastic/mcp-server-elasticsearch")
    print("  - pip install fastmcp openai fastapi uvicorn websockets")
    print("=" * 50)
    
    uvicorn.run(
        "run:app",
        host=CONFIG["server"]["host"],
        port=CONFIG["server"]["port"],
        reload=False
    )
