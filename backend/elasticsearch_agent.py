#!/usr/bin/env python3
"""
Elasticsearch Agent - 核心Agent类
"""

import asyncio
import json
import logging
import re
import sys
import os
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from fastmcp import Client
    from fastmcp.client.transports import NpxStdioTransport
except ImportError:
    print("请安装fastmcp: pip install fastmcp")
    sys.exit(1)

try:
    from openai import OpenAI
except ImportError:
    print("请安装openai: pip install openai")
    sys.exit(1)

from config.agent_config import config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ElasticsearchAgent:
    """Elasticsearch智能助手Agent"""
    
    def __init__(self):
        """初始化Agent"""
        self.llm_config = config.get_llm_config()
        self.mcp_config = config.get_mcp_config()
        self.agent_config = config.get_agent_config()
        
        # 初始化LLM客户端
        self.llm_client = OpenAI(
            base_url=self.llm_config["base_url"],
            api_key=self.llm_config["api_key"]
        )
        
        # 初始化MCP客户端
        self.mcp_client = None
        self.conversation_history = []
        
        # 可用工具
        self.available_tools = []
        
    async def initialize(self):
        """初始化Agent"""
        try:
            logger.info("🚀 初始化Elasticsearch Agent...")
            
            # 连接MCP服务器
            await self._connect_mcp()
            
            # 获取可用工具
            await self._load_tools()
            
            logger.info("✅ Agent初始化完成!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Agent初始化失败: {e}")
            return False
    
    async def _connect_mcp(self):
        """连接MCP服务器"""
        try:
            logger.info("🔗 连接到Elasticsearch MCP服务器...")

            # 获取MCP配置
            mcp_server_config = self.mcp_config["mcpServers"]["elasticsearch-mcp-server"]

            # 创建传输
            transport = NpxStdioTransport(
                package="@elastic/mcp-server-elasticsearch",
                args=["-y"]
            )
            transport.env = mcp_server_config["env"]

            # 创建MCP客户端
            self.mcp_client = Client(transport)
            await self.mcp_client.__aenter__()

            # 测试连接
            await self.mcp_client.ping()
            logger.info("✅ MCP服务器连接成功!")

        except Exception as e:
            logger.error(f"❌ MCP连接失败: {e}")
            raise
    
    async def _load_tools(self):
        """加载可用工具"""
        try:
            tools = await self.mcp_client.list_tools()
            self.available_tools = [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "schema": getattr(tool, 'inputSchema', {})
                }
                for tool in tools
            ]
            
            logger.info(f"📋 加载了 {len(self.available_tools)} 个工具:")
            for tool in self.available_tools:
                logger.info(f"  - {tool['name']}: {tool['description']}")
                
        except Exception as e:
            logger.error(f"❌ 加载工具失败: {e}")
            raise
    
    async def chat(self, user_message: str, stream: bool = False) -> AsyncGenerator[str, None]:
        """与Agent对话"""
        try:
            # 添加用户消息到历史
            self.conversation_history.append({
                "role": "user",
                "content": user_message,
                "timestamp": datetime.now().isoformat()
            })
            
            # 构建消息
            messages = self._build_messages()
            
            # 调用LLM
            if stream:
                async for chunk in self._stream_chat(messages):
                    yield chunk
            else:
                response = await self._complete_chat(messages)
                yield response
                
        except Exception as e:
            error_msg = f"❌ 对话处理失败: {e}"
            logger.error(error_msg)
            yield error_msg
    
    def _build_messages(self) -> List[Dict[str, str]]:
        """构建对话消息"""
        messages = [
            {
                "role": "system",
                "content": self.agent_config["system_prompt"]
            }
        ]
        
        # 添加工具信息
        tools_info = "\n\n**当前可用的Elasticsearch工具**:\n"
        for tool in self.available_tools:
            tools_info += f"- **{tool['name']}**: {tool['description']}\n"
        
        messages[0]["content"] += tools_info
        
        # 添加对话历史（保留最近的对话）
        recent_history = self.conversation_history[-self.agent_config["max_conversation_history"]:]
        for msg in recent_history:
            messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        
        return messages
    
    async def _stream_chat(self, messages: List[Dict[str, str]]) -> AsyncGenerator[str, None]:
        """流式对话"""
        try:
            # 使用 asyncio.to_thread 在线程池中运行同步的 OpenAI 调用
            def sync_stream_chat():
                completion = self.llm_client.chat.completions.create(
                    model=self.llm_config["model"],
                    messages=messages,
                    temperature=self.llm_config["temperature"],
                    max_tokens=self.llm_config["max_tokens"],
                    stream=True
                )

                full_response = ""
                chunks = []
                for chunk in completion:
                    # 尝试获取内容，优先使用 content，如果没有则使用 reasoning_content
                    content = None
                    if hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                    elif hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                        content = chunk.choices[0].delta.reasoning_content

                    if content:
                        full_response += content
                        chunks.append(content)

                return chunks, full_response

            # 在线程池中执行同步调用
            chunks, full_response = await asyncio.to_thread(sync_stream_chat)

            # 检查是否包含工具调用
            if "TOOL_CALL:" in full_response:
                # 处理工具调用
                async for result_chunk in self._handle_tool_calls(full_response):
                    yield result_chunk
            else:
                # 逐个返回内容块
                for content in chunks:
                    yield content

                # 保存完整回复到历史
                self.conversation_history.append({
                    "role": "assistant",
                    "content": full_response,
                    "timestamp": datetime.now().isoformat()
                })

        except Exception as e:
            logger.error(f"❌ 流式对话失败: {e}")
            yield f"❌ 对话失败: {e}"

    async def _handle_tool_calls(self, response: str) -> AsyncGenerator[str, None]:
        """处理工具调用"""
        try:
            import re
            import json

            # 更灵活的工具调用解析模式
            tool_pattern = r'TOOL_CALL:\s*(\w+)\s*PARAMETERS:\s*(\{.*?\})'
            matches = re.findall(tool_pattern, response, re.DOTALL)

            if not matches:
                yield response
                return

            # 执行工具调用
            for tool_name, params_str in matches:
                try:
                    yield f"\n🛠️ 正在执行工具: {tool_name}\n"

                    # 清理和修复 JSON 参数
                    params_str = params_str.strip()

                    # 尝试修复常见的 JSON 格式问题
                    params_str = self._fix_json_format(params_str)

                    logger.info(f"🔧 解析参数: {params_str}")

                    # 解析参数
                    parameters = json.loads(params_str)

                    # 执行工具
                    result = await self.execute_tool(tool_name, parameters)

                    if result["success"]:
                        yield "✅ 工具执行成功\n"
                        yield f"📊 查询结果:\n{result['result']}\n\n"

                        # 基于结果进行分析
                        analysis_prompt = f"""
基于以下Elasticsearch查询结果，请进行详细分析：

工具: {tool_name}
参数: {parameters}
结果: {result['result']}

请分析数据并提供有用的见解。
"""

                        # 调用LLM进行分析
                        analysis_messages = [
                            {"role": "system", "content": "你是一个专业的数据分析师，请基于提供的Elasticsearch查询结果进行详细分析。"},
                            {"role": "user", "content": analysis_prompt}
                        ]

                        async for analysis_chunk in self._get_analysis(analysis_messages):
                            yield analysis_chunk

                    else:
                        yield f"❌ 工具执行失败: {result['error']}\n"

                except json.JSONDecodeError as e:
                    yield f"❌ 参数解析失败: {e}\n"
                    yield f"🔍 原始参数: {params_str}\n"
                    # 尝试使用默认参数
                    yield "🔄 尝试使用默认参数...\n"
                    default_params = self._get_default_params(tool_name)
                    if default_params:
                        result = await self.execute_tool(tool_name, default_params)
                        if result["success"]:
                            yield "✅ 使用默认参数执行成功\n"
                            yield f"📊 查询结果:\n{result['result']}\n\n"
                        else:
                            yield f"❌ 默认参数执行失败: {result['error']}\n"
                except Exception as e:
                    yield f"❌ 工具调用失败: {e}\n"

            # 保存完整对话到历史
            self.conversation_history.append({
                "role": "assistant",
                "content": response,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"❌ 工具调用处理失败: {e}")
            yield f"❌ 工具调用处理失败: {e}"

    def _fix_json_format(self, json_str: str) -> str:
        """修复常见的JSON格式问题"""
        try:
            # 移除多余的空白字符
            json_str = json_str.strip()

            # 修复常见的引号问题
            json_str = json_str.replace("'", '"')

            # 修复尾随逗号问题
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)

            # 确保字段名被引号包围
            json_str = re.sub(r'(\w+):', r'"\1":', json_str)

            return json_str
        except Exception as e:
            logger.warning(f"JSON修复失败: {e}")
            return json_str

    def _get_default_params(self, tool_name: str) -> dict:
        """获取工具的默认参数"""
        defaults = {
            "list_indices": {"indexPattern": "*"},
            "search": {
                "index": "paas-k3s-filebeatk8s-index-*",
                "queryBody": {
                    "size": 10,
                    "sort": [{"@timestamp": {"order": "desc"}}],
                    "query": {
                        "bool": {
                            "must": [
                                {"range": {"@timestamp": {"gte": "now-1h"}}},
                                {"match": {"message": "exception"}}
                            ]
                        }
                    }
                }
            },
            "get_mappings": {"index": "paas-k3s-filebeatk8s-index-*"},
            "get_shards": {}
        }
        return defaults.get(tool_name, {})

    async def _get_analysis(self, messages: List[Dict[str, str]]) -> AsyncGenerator[str, None]:
        """获取分析结果"""
        try:
            def sync_analysis():
                completion = self.llm_client.chat.completions.create(
                    model=self.llm_config["model"],
                    messages=messages,
                    temperature=0.3,  # 降低温度以获得更准确的分析
                    max_tokens=2000,
                    stream=True
                )

                chunks = []
                for chunk in completion:
                    content = None
                    if hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                    elif hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                        content = chunk.choices[0].delta.reasoning_content

                    if content:
                        chunks.append(content)

                return chunks

            # 在线程池中执行同步调用
            chunks = await asyncio.to_thread(sync_analysis)

            # 逐个返回分析内容
            for content in chunks:
                yield content

        except Exception as e:
            logger.error(f"❌ 分析失败: {e}")
            yield f"❌ 分析失败: {e}"
    
    async def _complete_chat(self, messages: List[Dict[str, str]]) -> str:
        """完整对话"""
        try:
            # 使用 asyncio.to_thread 在线程池中运行同步的 OpenAI 调用
            def sync_complete_chat():
                response = self.llm_client.chat.completions.create(
                    model=self.llm_config["model"],
                    messages=messages,
                    temperature=self.llm_config["temperature"],
                    max_tokens=self.llm_config["max_tokens"]
                )
                return response.choices[0].message.content

            # 在线程池中执行同步调用
            assistant_message = await asyncio.to_thread(sync_complete_chat)

            # 保存回复到历史
            self.conversation_history.append({
                "role": "assistant",
                "content": assistant_message,
                "timestamp": datetime.now().isoformat()
            })

            return assistant_message

        except Exception as e:
            logger.error(f"❌ 完整对话失败: {e}")
            return f"❌ 对话失败: {e}"
    
    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行MCP工具"""
        try:
            logger.info(f"🛠️ 执行工具: {tool_name} with {parameters}")
            
            result = await self.mcp_client.call_tool(tool_name, parameters)
            
            return {
                "success": True,
                "result": result[0].text if result else None,
                "tool": tool_name,
                "parameters": parameters
            }
            
        except Exception as e:
            logger.error(f"❌ 工具执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool": tool_name,
                "parameters": parameters
            }
    
    async def get_indices(self) -> Dict[str, Any]:
        """获取索引列表"""
        return await self.execute_tool("list_indices", {"indexPattern": "*"})
    
    async def search_data(self, index: str, query: Dict[str, Any], size: int = 10) -> Dict[str, Any]:
        """搜索数据"""
        return await self.execute_tool("search", {
            "index": index,
            "queryBody": query,
            "size": size
        })
    
    async def get_mappings(self, index: str) -> Dict[str, Any]:
        """获取索引映射"""
        return await self.execute_tool("get_mappings", {"index": index})
    
    async def get_shards(self, index: str = None) -> Dict[str, Any]:
        """获取分片信息"""
        params = {}
        if index:
            params["index"] = index
        return await self.execute_tool("get_shards", params)
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def clear_conversation_history(self):
        """清空对话历史"""
        self.conversation_history = []
        logger.info("🗑️ 对话历史已清空")
    
    async def close(self):
        """关闭Agent"""
        try:
            if self.mcp_client:
                await self.mcp_client.__aexit__(None, None, None)
            logger.info("🔌 Agent已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭Agent失败: {e}")

# 全局Agent实例
agent = ElasticsearchAgent()

async def main():
    """测试Agent"""
    print("🧪 测试Elasticsearch Agent")
    print("=" * 50)
    
    # 初始化
    if await agent.initialize():
        print("✅ Agent初始化成功!")
        
        # 测试对话
        print("\n💬 测试对话...")
        async for response in agent.chat("请帮我列出所有的Elasticsearch索引"):
            print(response, end="")
        
        print("\n\n🎉 测试完成!")
    else:
        print("❌ Agent初始化失败!")
    
    await agent.close()

if __name__ == "__main__":
    asyncio.run(main())
