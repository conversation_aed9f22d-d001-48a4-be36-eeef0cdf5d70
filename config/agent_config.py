#!/usr/bin/env python3
"""
Agent配置文件
"""

import os
from typing import Dict, Any

class AgentConfig:
    """Agent配置类"""
    
    def __init__(self):
        # LLM配置
        self.llm_config = {
            "base_url": "http://ai.ai.iot.chinamobile.com/imaas/v1",
            "api_key": "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db",
            "model": "DeepSeek-R1",  # 根据API示例更新模型名称
            "temperature": 0.7,
            "max_tokens": 4000,
            "stream": True  # 支持流式输出
        }
        
        # MCP配置
        self.mcp_config = {
            "mcpServers": {
                "elasticsearch-mcp-server": {
                    "command": "npx",
                    "args": [
                        "-y",
                        "@elastic/mcp-server-elasticsearch"
                    ],
                    "env": {
                        "ES_URL": "https://medusa.iotxmm.com:19203",
                        "ES_USERNAME": "elastic",
                        "ES_PASSWORD": "elastic$2020$search",
                        "ES_VERSION": "6"
                    },
                    "timeout": 600
                }
            }
        }
        
        # Agent配置
        self.agent_config = {
            "name": "Elasticsearch Assistant",
            "description": "专业的Elasticsearch数据查询和分析助手",
            "system_prompt": self._get_system_prompt(),
            "max_conversation_history": 20,
            "enable_streaming": True
        }
        
        # 服务器配置
        self.server_config = {
            "host": "0.0.0.0",
            "port": 8080,
            "debug": True,
            "cors_origins": ["http://localhost:3000", "http://127.0.0.1:3000"]
        }
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个专业的Elasticsearch数据查询和分析助手。你必须使用可用的工具来查询实际数据，而不是仅仅提供理论分析。

**重要：工具调用规则**
当用户请求查询数据时，你必须：
1. 首先调用相应的工具获取实际数据
2. 基于实际查询结果进行分析和回答
3. 不要在没有查询实际数据的情况下给出假设性回答

**工具调用格式**
使用以下格式调用工具：
```
TOOL_CALL: tool_name
PARAMETERS: {参数JSON}
```

**可用工具详情**：
- **list_indices**: 列出所有可用的Elasticsearch索引
  参数: {"indexPattern": "索引模式，如*或具体索引名"}

- **get_mappings**: 获取索引的字段映射信息
  参数: {"index": "索引名"}

- **search**: 执行Elasticsearch搜索查询
  参数: {"index": "索引名", "queryBody": {查询DSL}, "size": 返回条数}

- **get_shards**: 获取索引的分片信息
  参数: {"index": "索引名"} 或 {} 获取所有索引

**查询示例**：
用户问："查询索引paas-k3s-*最近1小时包含exception的日志"
你应该：
1. 调用search工具，构建包含时间范围和关键词的查询DSL
2. 分析返回的实际日志数据
3. 基于实际结果给出分析

**DSL查询示例**：
```json
{
  "size": 10,
  "sort": [{"@timestamp": {"order": "desc"}}],
  "query": {
    "bool": {
      "must": [
        {"range": {"@timestamp": {"gte": "now-1h"}}},
        {"match": {"message": "exception"}}
      ]
    }
  }
}
```

记住：始终先查询实际数据，再进行分析！"""

    def get_llm_config(self) -> Dict[str, Any]:
        """获取LLM配置"""
        return self.llm_config.copy()
    
    def get_mcp_config(self) -> Dict[str, Any]:
        """获取MCP配置"""
        return self.mcp_config.copy()
    
    def get_agent_config(self) -> Dict[str, Any]:
        """获取Agent配置"""
        return self.agent_config.copy()
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return self.server_config.copy()

# 全局配置实例
config = AgentConfig()

if __name__ == "__main__":
    # 测试配置
    print("🧪 Agent配置测试")
    print("=" * 50)
    
    print("LLM配置:")
    llm_config = config.get_llm_config()
    for key, value in llm_config.items():
        if "api_key" in key:
            print(f"  {key}: {value[:10]}...")
        else:
            print(f"  {key}: {value}")
    
    print("\nMCP配置:")
    mcp_config = config.get_mcp_config()
    print(f"  服务器数量: {len(mcp_config['mcpServers'])}")
    for name in mcp_config['mcpServers'].keys():
        print(f"  - {name}")
    
    print("\nAgent配置:")
    agent_config = config.get_agent_config()
    print(f"  名称: {agent_config['name']}")
    print(f"  描述: {agent_config['description']}")
    
    print("\n✅ 配置加载成功!")
