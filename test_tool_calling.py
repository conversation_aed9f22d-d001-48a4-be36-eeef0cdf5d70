#!/usr/bin/env python3
"""
测试工具调用功能
"""

import asyncio
import json
import websockets

async def test_websocket_tool_calling():
    """测试WebSocket工具调用"""
    print("🧪 测试WebSocket工具调用")
    print("=" * 50)
    
    uri = "ws://localhost:8080/ws"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 测试消息：要求查询索引
            test_message = "查询索引paas-k3s-filebeatk8s-index-*最近一个小时的日志中包含exception的内容,并分析可能的原因"
            
            print(f"📤 发送测试消息: {test_message}")
            
            # 发送消息
            await websocket.send(json.dumps({
                "message": test_message
            }))
            
            print("📥 接收回复:")
            print("-" * 30)
            
            # 接收回复
            while True:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                    data = json.loads(response)
                    
                    if data.get("type") == "user_message":
                        print(f"👤 用户: {data.get('content', '')}")
                    elif data.get("type") == "assistant_start":
                        print("🤖 助手开始回复...")
                    elif data.get("type") == "assistant_chunk":
                        print(data.get("content", ""), end="", flush=True)
                    elif data.get("type") == "assistant_end":
                        print("\n🎉 回复完成!")
                        break
                        
                except asyncio.TimeoutError:
                    print("\n⏰ 响应超时")
                    break
                except websockets.exceptions.ConnectionClosed:
                    print("\n🔌 连接已关闭")
                    break
                    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def test_http_tool_calling():
    """测试HTTP工具调用"""
    print("\n🧪 测试HTTP工具调用")
    print("=" * 50)
    
    import httpx
    
    try:
        async with httpx.AsyncClient() as client:
            # 测试消息
            test_message = "请列出所有可用的Elasticsearch索引"
            
            print(f"📤 发送HTTP请求: {test_message}")
            
            response = await client.post(
                "http://localhost:8080/chat",
                json={
                    "message": test_message,
                    "stream": False
                },
                timeout=30.0
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ HTTP请求成功")
                print("📥 回复:")
                print("-" * 30)
                print(result.get("response", ""))
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
    except Exception as e:
        print(f"❌ HTTP测试失败: {e}")

async def main():
    """主函数"""
    print("🔍 开始测试工具调用功能")
    print("=" * 60)
    
    # 测试WebSocket
    await test_websocket_tool_calling()
    
    # 等待一下
    await asyncio.sleep(2)
    
    # 测试HTTP
    await test_http_tool_calling()
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
