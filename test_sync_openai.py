#!/usr/bin/env python3
"""
测试同步 OpenAI 调用
"""

from openai import OpenAI

# 使用你提供的配置
client = OpenAI(
    api_key='sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db', 
    base_url='http://ai.ai.iot.chinamobile.com/imaas/v1'
)

print("🧪 测试同步 OpenAI 调用")
print("=" * 50)

try:
    print("🤖 发送测试消息...")
    print("用户: 你是谁？")
    print("回复: ", end="", flush=True)
    
    # 流式输出方式
    completion = client.chat.completions.create(
        model="DeepSeek-R1",
        messages=[
            {'role': 'system', 'content': 'You are a helpful assistant.'},
            {'role': 'user', 'content': '你是谁？'}
        ],
        stream=True
    )
    
    full_response = ""
    for chunk in completion:
        # 打印原始 chunk 数据（用于调试）
        # print(f"\n[DEBUG] {chunk.model_dump_json()}")
        
        if chunk.choices[0].delta.content:
            content = chunk.choices[0].delta.content
            print(content, end="", flush=True)
            full_response += content
    
    print(f"\n\n✅ 同步调用成功!")
    print(f"📏 完整回复长度: {len(full_response)} 字符")
    
except Exception as e:
    print(f"\n❌ 同步调用失败: {e}")
    print(f"错误类型: {type(e).__name__}")
    
    # 打印详细错误信息
    if hasattr(e, 'response'):
        print(f"HTTP 状态码: {e.response.status_code}")
        print(f"响应内容: {e.response.text}")
