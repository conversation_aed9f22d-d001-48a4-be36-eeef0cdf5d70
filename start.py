#!/usr/bin/env python3
"""
Elasticsearch Agent启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    try:
        import fastmcp
        import fastapi
        import uvicorn
        import openai
        print("✅ Python依赖已安装")
    except ImportError as e:
        print(f"❌ 缺少Python依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    # 检查npm包
    try:
        result = subprocess.run(
            ["npm", "list", "-g", "@elastic/mcp-server-elasticsearch"],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("✅ Elasticsearch MCP服务器已安装")
        else:
            print("⚠️ Elasticsearch MCP服务器未安装")
            print("请运行: npm install -g @elastic/mcp-server-elasticsearch")
            return False
    except FileNotFoundError:
        print("❌ 未找到npm，请先安装Node.js")
        return False
    
    return True

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    # 切换到backend目录
    backend_dir = Path(__file__).parent / "backend"
    os.chdir(backend_dir)
    
    # 启动FastAPI服务器
    try:
        subprocess.Popen([
            sys.executable, "api_server.py"
        ])
        print("✅ 后端服务启动成功")
        return True
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return False

def start_frontend():
    """启动前端服务"""
    print("🌐 启动前端服务...")
    
    frontend_dir = Path(__file__).parent / "frontend"
    frontend_file = frontend_dir / "index.html"
    
    if frontend_file.exists():
        # 启动简单的HTTP服务器
        try:
            os.chdir(frontend_dir)
            subprocess.Popen([
                sys.executable, "-m", "http.server", "3000"
            ])
            print("✅ 前端服务启动成功")
            print("📱 前端地址: http://localhost:3000")
            return True
        except Exception as e:
            print(f"❌ 前端服务启动失败: {e}")
            return False
    else:
        print("❌ 前端文件不存在")
        return False

def open_browser():
    """打开浏览器"""
    print("🌐 打开浏览器...")
    try:
        webbrowser.open("http://localhost:3000")
        print("✅ 浏览器已打开")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print("请手动访问: http://localhost:3000")

def main():
    """主函数"""
    print("🚀 Elasticsearch Agent 启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装依赖")
        return
    
    print("\n📋 启动服务...")
    
    # 启动后端
    if not start_backend():
        print("❌ 后端启动失败")
        return
    
    # 等待后端启动
    print("⏳ 等待后端服务启动...")
    time.sleep(3)
    
    # 启动前端
    if not start_frontend():
        print("❌ 前端启动失败")
        return
    
    # 等待前端启动
    print("⏳ 等待前端服务启动...")
    time.sleep(2)
    
    # 打开浏览器
    open_browser()
    
    print("\n🎉 所有服务启动完成!")
    print("=" * 50)
    print("📊 后端API: http://localhost:8080")
    print("📊 API文档: http://localhost:8080/docs")
    print("🌐 前端界面: http://localhost:3000")
    print("=" * 50)
    print("\n💡 提示:")
    print("- 确保Elasticsearch服务器可访问")
    print("- 检查网络连接")
    print("- 如有问题请查看控制台日志")
    print("\n按 Ctrl+C 停止服务")
    
    try:
        # 保持脚本运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n👋 正在停止服务...")
        print("✅ 服务已停止")

if __name__ == "__main__":
    main()
