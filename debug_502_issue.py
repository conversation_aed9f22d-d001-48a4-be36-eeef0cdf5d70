#!/usr/bin/env python3
"""
诊断和修复 502 错误的脚本
"""

import asyncio
import json
import httpx
from openai import AsyncOpenAI
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# API 配置
CONFIG = {
    "base_url": "http://ai.ai.iot.chinamobile.com/imaas/v1",
    "api_key": "sk-y3dYMll9oWtQNgj2I9dKO8UAHS24tcMgr6alaDl0EWfqU5Db",
    "model": "DeepSeek-R1"
}

async def test_direct_httpx():
    """使用 httpx 直接测试 API"""
    print("🧪 测试 1: 使用 httpx 直接调用")
    print("=" * 50)
    
    try:
        # 创建自定义的 httpx 客户端，模拟 curl 的行为
        timeout = httpx.Timeout(60.0, connect=10.0)
        limits = httpx.Limits(max_keepalive_connections=5, max_connections=10)
        
        async with httpx.AsyncClient(
            timeout=timeout,
            limits=limits,
            follow_redirects=True,
            verify=False  # 如果有 SSL 问题
        ) as client:
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Authorization": f"Bearer {CONFIG['api_key']}",
                "User-Agent": "httpx/0.28.1"  # 明确设置 User-Agent
            }
            
            data = {
                "model": CONFIG["model"],
                "stream": True,
                "messages": [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "你好"}
                ]
            }
            
            print(f"📡 请求 URL: {CONFIG['base_url']}/chat/completions")
            print(f"🔑 API Key: {CONFIG['api_key'][:10]}...")
            print(f"📋 请求头: {headers}")
            
            response = await client.post(
                f"{CONFIG['base_url']}/chat/completions",
                json=data,
                headers=headers
            )
            
            print(f"📊 响应状态: {response.status_code}")
            print(f"📋 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                print("✅ httpx 调用成功!")
                # 处理流式响应
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            chunk_data = json.loads(data_str)
                            if "choices" in chunk_data:
                                delta = chunk_data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    print(delta["content"], end="", flush=True)
                        except:
                            continue
                print("\n")
            else:
                print(f"❌ httpx 调用失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                
    except Exception as e:
        print(f"❌ httpx 测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")

async def test_openai_sdk_with_custom_client():
    """使用自定义 httpx 客户端的 OpenAI SDK"""
    print("\n🧪 测试 2: 使用自定义 httpx 客户端的 OpenAI SDK")
    print("=" * 50)
    
    try:
        # 创建自定义的 httpx 客户端
        timeout = httpx.Timeout(60.0, connect=10.0)
        limits = httpx.Limits(max_keepalive_connections=5, max_connections=10)
        
        custom_client = httpx.AsyncClient(
            timeout=timeout,
            limits=limits,
            follow_redirects=True,
            verify=False
        )
        
        # 使用自定义客户端创建 OpenAI 客户端
        openai_client = AsyncOpenAI(
            base_url=CONFIG["base_url"],
            api_key=CONFIG["api_key"],
            http_client=custom_client
        )
        
        print("🤖 使用自定义 httpx 客户端测试 OpenAI SDK...")
        
        response = await openai_client.chat.completions.create(
            model=CONFIG["model"],
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "你好"}
            ],
            stream=True
        )
        
        print("✅ OpenAI SDK 调用成功!")
        print("回复: ", end="", flush=True)
        
        async for chunk in response:
            if chunk.choices[0].delta.content:
                print(chunk.choices[0].delta.content, end="", flush=True)
        
        print("\n")
        await custom_client.aclose()
        
    except Exception as e:
        print(f"❌ OpenAI SDK 测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")

async def test_openai_sdk_default():
    """使用默认配置的 OpenAI SDK"""
    print("\n🧪 测试 3: 使用默认配置的 OpenAI SDK")
    print("=" * 50)
    
    try:
        client = AsyncOpenAI(
            base_url=CONFIG["base_url"],
            api_key=CONFIG["api_key"],
            timeout=60.0  # 增加超时时间
        )
        
        print("🤖 使用默认配置测试 OpenAI SDK...")
        
        response = await client.chat.completions.create(
            model=CONFIG["model"],
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "你好"}
            ],
            stream=True
        )
        
        print("✅ OpenAI SDK 默认配置调用成功!")
        print("回复: ", end="", flush=True)
        
        async for chunk in response:
            if chunk.choices[0].delta.content:
                print(chunk.choices[0].delta.content, end="", flush=True)
        
        print("\n")
        
    except Exception as e:
        print(f"❌ OpenAI SDK 默认配置测试失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 打印详细错误信息
        if hasattr(e, 'response'):
            print(f"HTTP 状态码: {e.response.status_code}")
            print(f"响应内容: {e.response.text}")

async def main():
    """主函数"""
    print("🔍 开始诊断 502 错误")
    print("=" * 60)
    
    await test_direct_httpx()
    await test_openai_sdk_with_custom_client()
    await test_openai_sdk_default()
    
    print("\n💡 建议的解决方案:")
    print("1. 增加超时时间配置")
    print("2. 使用自定义 httpx 客户端")
    print("3. 添加重试机制")
    print("4. 检查网络连接和防火墙设置")

if __name__ == "__main__":
    asyncio.run(main())
