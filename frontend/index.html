<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elasticsearch Agent - 智能数据助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .container {
            width: 95%;
            max-width: 1200px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            background: #343a40;
            color: white;
            text-align: center;
        }

        .sidebar-header h1 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 12px;
            opacity: 0.8;
        }

        .tools-section {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }

        .tools-section h3 {
            margin-bottom: 15px;
            color: #495057;
            font-size: 14px;
        }

        .tool-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .tool-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.1);
        }

        .tool-name {
            font-weight: 600;
            color: #007bff;
            font-size: 13px;
        }

        .tool-desc {
            font-size: 11px;
            color: #6c757d;
            margin-top: 4px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }

        .message.user .message-avatar {
            background: #007bff;
        }

        .message.assistant .message-avatar {
            background: #28a745;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            line-height: 1.4;
            font-size: 14px;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-group {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-field {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 22px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s;
        }

        .input-field:focus {
            border-color: #007bff;
        }

        .send-button {
            width: 44px;
            height: 44px;
            border: none;
            background: #007bff;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .send-button:hover:not(:disabled) {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 10px;
        }

        .typing-dots {
            display: flex;
            gap: 2px;
        }

        .typing-dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #6c757d;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { opacity: 0.3; }
            30% { opacity: 1; }
        }

        .quick-actions {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }

        .quick-action {
            padding: 6px 12px;
            background: #e9ecef;
            border: none;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .quick-action:hover {
            background: #dee2e6;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 12px;
            font-size: 13px;
        }

        @media (max-width: 768px) {
            .container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .sidebar {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>🔍 ES Agent</h1>
                <p>智能数据查询助手</p>
            </div>
            
            <div class="tools-section">
                <h3>🛠️ 可用工具</h3>
                <div id="tools-list">
                    <div class="tool-item">
                        <div class="tool-name">正在加载...</div>
                        <div class="tool-desc">请稍候</div>
                    </div>
                </div>
                
                <h3 style="margin-top: 20px;">📊 快速操作</h3>
                <div class="tool-item" onclick="sendQuickMessage('请列出所有的Elasticsearch索引')">
                    <div class="tool-name">列出索引</div>
                    <div class="tool-desc">查看所有可用索引</div>
                </div>
                
                <div class="tool-item" onclick="sendQuickMessage('请帮我分析集群的健康状态')">
                    <div class="tool-name">集群状态</div>
                    <div class="tool-desc">检查集群健康</div>
                </div>
                
                <div class="tool-item" onclick="sendQuickMessage('请帮我搜索最近的日志数据')">
                    <div class="tool-name">搜索日志</div>
                    <div class="tool-desc">查询最新日志</div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 聊天头部 -->
            <div class="chat-header">
                <h2>Elasticsearch 智能助手</h2>
                <div class="status-indicator">
                    <div class="status-dot" id="status-dot"></div>
                    <span id="status-text">连接中...</span>
                </div>
            </div>

            <!-- 聊天容器 -->
            <div class="chat-container" id="chat-container">
                <div class="message assistant">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        你好！我是Elasticsearch智能助手。我可以帮助你：<br><br>
                        📊 查询和分析Elasticsearch数据<br>
                        🔍 执行复杂的搜索查询<br>
                        📈 提供数据统计和分析<br>
                        🛠️ 管理索引和映射<br><br>
                        请告诉我你想要查询什么数据！
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-container">
                <div class="quick-actions">
                    <button class="quick-action" onclick="sendQuickMessage('列出所有索引')">列出索引</button>
                    <button class="quick-action" onclick="sendQuickMessage('搜索最近1小时的数据')">最近数据</button>
                    <button class="quick-action" onclick="sendQuickMessage('分析错误日志')">错误分析</button>
                    <button class="quick-action" onclick="clearChat()">清空对话</button>
                </div>
                
                <div class="typing-indicator" id="typing-indicator">
                    <span>AI正在思考</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
                
                <div class="input-group">
                    <textarea 
                        id="message-input" 
                        class="input-field" 
                        placeholder="请输入你的问题..."
                        rows="1"
                    ></textarea>
                    <button id="send-button" class="send-button" onclick="sendMessage()">
                        ➤
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 配置
        const API_BASE_URL = 'http://localhost:8080';
        let ws = null;
        let isConnected = false;

        // DOM元素
        const chatContainer = document.getElementById('chat-container');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const statusDot = document.getElementById('status-dot');
        const statusText = document.getElementById('status-text');
        const typingIndicator = document.getElementById('typing-indicator');
        const toolsList = document.getElementById('tools-list');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            setupEventListeners();
        });

        async function initializeApp() {
            try {
                // 检查API状态
                await checkApiStatus();

                // 加载工具列表
                await loadTools();

                // 连接WebSocket
                connectWebSocket();

            } catch (error) {
                console.error('初始化失败:', error);
                showError('连接失败，请检查后端服务是否启动');
            }
        }

        async function checkApiStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();

                if (data.status === 'healthy') {
                    updateStatus(true, '已连接');
                } else {
                    throw new Error('API不健康');
                }
            } catch (error) {
                updateStatus(false, '连接失败');
                throw error;
            }
        }

        async function loadTools() {
            try {
                const response = await fetch(`${API_BASE_URL}/tools`);
                const data = await response.json();

                displayTools(data.tools);
            } catch (error) {
                console.error('加载工具失败:', error);
            }
        }

        function displayTools(tools) {
            toolsList.innerHTML = '';

            tools.forEach(tool => {
                const toolElement = document.createElement('div');
                toolElement.className = 'tool-item';
                toolElement.innerHTML = `
                    <div class="tool-name">${tool.name}</div>
                    <div class="tool-desc">${tool.description}</div>
                `;
                toolsList.appendChild(toolElement);
            });
        }

        function connectWebSocket() {
            try {
                ws = new WebSocket(`ws://localhost:8080/ws`);

                ws.onopen = function() {
                    console.log('WebSocket连接成功');
                    isConnected = true;
                    updateStatus(true, '已连接');
                };

                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };

                ws.onclose = function() {
                    console.log('WebSocket连接关闭');
                    isConnected = false;
                    updateStatus(false, '连接断开');
                };

                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    updateStatus(false, '连接错误');
                };

            } catch (error) {
                console.error('WebSocket连接失败:', error);
                updateStatus(false, '连接失败');
            }
        }

        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'user_message':
                    // 用户消息已确认
                    break;

                case 'assistant_start':
                    hideTyping();
                    currentAssistantMessage = addMessage('assistant', '');
                    break;

                case 'assistant_chunk':
                    if (currentAssistantMessage) {
                        appendToMessage(currentAssistantMessage, data.content);
                    }
                    break;

                case 'assistant_end':
                    currentAssistantMessage = null;
                    enableInput();
                    break;
            }
        }

        function setupEventListeners() {
            // 发送按钮
            sendButton.addEventListener('click', sendMessage);

            // 输入框回车发送
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // 自动调整输入框高度
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        }

        let currentAssistantMessage = null;

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || !isConnected) return;

            // 添加用户消息
            addMessage('user', message);

            // 清空输入框
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // 显示输入状态
            showTyping();
            disableInput();

            // 发送消息
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ message: message }));
            } else {
                // 备用HTTP方式
                sendMessageHttp(message);
            }
        }

        function sendQuickMessage(message) {
            messageInput.value = message;
            sendMessage();
        }

        async function sendMessageHttp(message) {
            try {
                const response = await fetch(`${API_BASE_URL}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        stream: false
                    })
                });

                const data = await response.json();
                hideTyping();
                addMessage('assistant', data.response);
                enableInput();

            } catch (error) {
                console.error('发送消息失败:', error);
                hideTyping();
                showError('发送消息失败，请重试');
                enableInput();
            }
        }

        function addMessage(role, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;

            const avatar = role === 'user' ? '👤' : '🤖';

            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">${formatMessage(content)}</div>
            `;

            chatContainer.appendChild(messageDiv);
            scrollToBottom();

            return messageDiv.querySelector('.message-content');
        }

        function appendToMessage(messageElement, content) {
            messageElement.innerHTML += content;
            scrollToBottom();
        }

        function formatMessage(content) {
            // 简单的Markdown格式化
            return content
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/`(.*?)`/g, '<code>$1</code>')
                .replace(/\n/g, '<br>');
        }

        function showTyping() {
            typingIndicator.style.display = 'flex';
        }

        function hideTyping() {
            typingIndicator.style.display = 'none';
        }

        function disableInput() {
            messageInput.disabled = true;
            sendButton.disabled = true;
        }

        function enableInput() {
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();
        }

        function updateStatus(connected, text) {
            statusDot.style.background = connected ? '#28a745' : '#dc3545';
            statusText.textContent = text;
        }

        function scrollToBottom() {
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;

            const inputContainer = document.querySelector('.input-container');
            inputContainer.insertBefore(errorDiv, inputContainer.firstChild);

            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }

        function clearChat() {
            if (confirm('确定要清空对话历史吗？')) {
                // 清空界面
                const messages = chatContainer.querySelectorAll('.message');
                messages.forEach((msg, index) => {
                    if (index > 0) { // 保留欢迎消息
                        msg.remove();
                    }
                });

                // 清空服务器端历史
                fetch(`${API_BASE_URL}/conversation/history`, {
                    method: 'DELETE'
                }).catch(console.error);
            }
        }

        // 全局函数
        window.sendQuickMessage = sendQuickMessage;
        window.clearChat = clearChat;
    </script>
</body>
</html>
